# -*- coding: utf-8 -*-

import sys
import os
import subprocess

exit_code = subprocess.call(
    ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe', '-W', 'ignore', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\INLINE~1\\bin\\scons.py', '--quiet', '-f', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\BACKEN~1.SCO', '--jobs', '16', '--warn=no-deprecated', '--no-site-dir', 'nuitka_src=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build', 'python_version=3.9', 'python_prefix=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39', 'experimental=', 'debug_modes=', 'deployment=false', 'no_deployment=', 'console_mode=disable', 'lto_mode=no', 'noelf_mode=true', 'cpp_defines=_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1', 'target_arch=x86_64', 'module_mode=false', 'dll_mode=true', 'exe_mode=false', 'standalone_mode=true', 'onefile_mode=true', 'onefile_temp_mode=true', 'gil_mode=true', 'source_dir=.', 'nuitka_python=false', 'debug_mode=false', 'debugger_mode=false', 'python_debug=false', 'full_compat=false', 'trace_mode=false', 'file_reference_mode=runtime', 'compiled_module_count=387', 'result_exe=D:\\jiedan\\74014~1.5AI\\APP~1.DIS\\app.dll', 'frozen_modules=153', 'python_sysflag_no_site=true'],
    env={'ALLUSERSPROFILE': 'C:\\ProgramData','AMDRMSDKPATH': 'C:\\Program Files\\AMD\\RyzenMasterSDK\\','APPDATA': 'C:\\Users\\<USER>\\AppData\\Roaming','COMMONPROGRAMFILES': 'C:\\Program Files\\Common Files','COMMONPROGRAMFILES(X86)': 'C:\\Program Files (x86)\\Common Files','COMMONPROGRAMW6432': 'C:\\Program Files\\Common Files','COMPUTERNAME': '大清早的大宝贝','COMSPEC': 'C:\\Windows\\system32\\cmd.exe','DRIVERDATA': 'C:\\Windows\\System32\\Drivers\\DriverData','EFC_7620': '1','FPS_BROWSER_APP_PROFILE_STRING': 'Internet Explorer','FPS_BROWSER_USER_PROFILE_STRING': 'Default','HOMEDRIVE': 'C:','HOMEPATH': '\\Users\\Administrator','INTELLIJ IDEA': 'D:\\IntelliJ IDEA 2019.2.4\\bin;','JAVA_HOME': 'C:\\Program Files\\Java\\jdk1.8.0_111','LITELOADERQQNT_PROFILE': 'D:\\Documents\\LiteloaderQQNT','LOCALAPPDATA': 'C:\\Users\\<USER>\\AppData\\Local','LOGONSERVER': '\\\\大清早的大宝贝','M2_HOME': 'D:\\maven-3.6.3','NUMBER_OF_PROCESSORS': '16','OS': 'Windows_NT','PATH': 'D:\\ImageMagick-7.1.2-Q16-HDRI;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Cursor\\resources\\app\\bin;C:\\ProgramData\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\;D:\\WinSCP\\;D:\\xshell\\;D:\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\cursor\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;d:\\cursor\\cursor\\resources\\app\\bin;C:\\MinGW\\bin;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;D:\\maven-3.6.3\\bin;C:\\Program Files\\cursor\\resources\\app\\bin;%pyenv%\\bin;%pyenv%\\shims;C:\\Tools\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\IntelliJ IDEA 2019.2.4\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;D:\\pyenv\\pyenv-win\\bin;D:\\pyenv\\pyenv-win\\shims;','PATHEXT': '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC','PROCESSOR_ARCHITECTURE': 'AMD64','PROCESSOR_IDENTIFIER': 'AMD64 Family 26 Model 68 Stepping 0, AuthenticAMD','PROCESSOR_LEVEL': '26','PROCESSOR_REVISION': '4400','PROGRAMDATA': 'C:\\ProgramData','PROGRAMFILES': 'C:\\Program Files','PROGRAMFILES(X86)': 'C:\\Program Files (x86)','PROGRAMW6432': 'C:\\Program Files','PROMPT': '$P$G','PSMODULEPATH': 'C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules','PT8HOME': 'D:\\Cisco Packet Tracer 8.1.1','PUBLIC': 'C:\\Users\\<USER>\\pyenv\\pyenv-win','PYTHONHASHSEED': '0','SESSIONNAME': 'Console','SYSTEMDRIVE': 'C:','SYSTEMROOT': 'C:\\Windows','TEMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','TMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','USERDOMAIN': '大清早的大宝贝','USERDOMAIN_ROAMINGPROFILE': '大清早的大宝贝','USERNAME': 'Administrator','USERPROFILE': 'C:\\Users\\<USER>\\Windows','NUITKA_PYTHON_EXE_PATH': 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe','NUITKA_PACKAGE_DIR': 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\nuitka','NUITKA_CACHE_DIR_DOWNLOADS': 'C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1','_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT': '5000','_NUITKA_BUILD_DEFINITIONS_CATALOG': '_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT,_NUITKA_BUILD_DEFINITIONS_CATALOG','NUITKA_QUIET': '0'},
    shell=False
)