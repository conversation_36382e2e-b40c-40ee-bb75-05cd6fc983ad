
================================================================================
时间: 2025-08-06 23:48:57
操作: 测试FFmpeg错误
返回码: 1
命令: ffmpeg -y -i nonexistent_file.mp4 -c:v libx264 output.mp4
错误信息:
ffmpeg: error while loading shared libraries: libavcodec.so.58: cannot open shared object file: No such file or directory
================================================================================

================================================================================
时间: 2025-08-06 23:48:57
操作: 测试操作
错误: 这是一个测试错误
详细信息: 详细信息：测试错误日志功能
================================================================================

================================================================================
时间: 2025-08-06 23:48:57
操作: 视频编码
问题: 进度监控超时
超时时长: 300.5秒
最后进度: 最后进度: 编码中 45.2%
================================================================================

================================================================================
时间: 2025-08-06 23:48:57
操作: 直接FFmpeg测试
返回码: 4294967294
命令: d:\jiedan\7.5 AI配音\ffmpeg\bin\ffmpeg.exe -y -i definitely_nonexistent_file.mp4 -c:v libx264 test_output.mp4
错误信息:
ffmpeg version 2025-07-01-git-11d1b71c31-essentials_build-www.gyan.dev Copyright (c) 2000-2025 the FFmpeg developers
  built with gcc 15.1.0 (Rev4, Built by MSYS2 project)
  configuration: --enable-gpl --enable-version3 --enable-static --disable-w32threads --disable-autodetect --enable-fontconfig --enable-iconv --enable-gnutls --enable-libxml2 --enable-gmp --enable-bzlib --enable-lzma --enable-zlib --enable-libsrt --enable-libssh --enable-libzmq --enable-avisynth --enable-sdl2 --enable-libwebp --enable-libx264 --enable-libx265 --enable-libxvid --enable-libaom --enable-libopenjpeg --enable-libvpx --enable-mediafoundation --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-libvidstab --enable-libvmaf --enable-libzimg --enable-amf --enable-cuda-llvm --enable-cuvid --enable-dxva2 --enable-d3d11va --enable-d3d12va --enable-ffnvcodec --enable-libvpl --enable-nvdec --enable-nvenc --enable-vaapi --enable-openal --enable-libgme --enable-libopenmpt --enable-libopencore-amrwb --enable-libmp3lame --enable-libtheora --enable-libvo-amrwbenc --enable-libgsm --enable-libopencore-amrnb --enable-libopus --enable-libspeex --enable-libvorbis --enable-librubberband
  libavutil      60.  4.101 / 60.  4.101
  libavcodec     62.  4.103 / 62.  4.103
  libavformat    62.  1.101 / 62.  1.101
  libavdevice    62.  0.100 / 62.  0.100
  libavfilter    11.  0.100 / 11.  0.100
  libswscale      9.  0.100 /  9.  0.100
  libswresample   6.  0.100 /  6.  0.100
[in#0 @ 000001465d243b00] Error opening input: No such file or directory
Error opening input file definitely_nonexistent_file.mp4.
Error opening input files: No such file or directory

================================================================================

================================================================================
时间: 2025-08-07 01:07:25
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:27
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:28
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:29
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:30
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:31
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:32
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:33
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:34
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:35
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:36
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:37
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:38
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:39
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:40
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:41
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:42
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:43
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:44
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:45
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:46
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:47
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:48
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:49
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: None args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.e...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:50
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: 1 args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.exe'...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:51
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: 1 args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.exe'...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:52
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: 1 args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.exe'...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:53
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: 1 args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.exe'...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:54
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: 1 args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.exe'...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:55
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: 1 args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.exe'...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:56
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: 1 args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.exe'...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:57
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: 1 args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.exe'...>
================================================================================

================================================================================
时间: 2025-08-07 01:07:58
操作: 预编码_监控
错误: stat: path should be string, bytes, os.PathLike or integer, not Popen
详细信息: 进度文件: <Popen: returncode: 1 args: ['d:\\jiedan\\7.5 AI配音\\ffmpeg\\bin\\ffmpeg.exe'...>
================================================================================

================================================================================
时间: 2025-08-07 01:30:00
操作: 预编码
问题: 进度监控超时
超时时长: 120.2秒
最后进度: 预编码: 14.0% (速度: 59.9x) (预计剩余: 00:19)
================================================================================

================================================================================
时间: 2025-08-07 01:44:44
操作: 预编码
问题: 进度监控超时
超时时长: 120.3秒
最后进度: 预编码: 14.0% (速度: 60.4x) (预计剩余: 00:19)
================================================================================

================================================================================
时间: 2025-08-07 01:51:10
操作: 预编码
问题: 进度监控超时
超时时长: 120.3秒
最后进度: 预编码: 13.9% (速度: 59.7x) (预计剩余: 00:19)
================================================================================

================================================================================
时间: 2025-08-07 02:02:13
操作: 基础视频编码
返回码: 15
命令: d:\jiedan\7.5 AI配音\ffmpeg\bin\ffmpeg.exe -y -progress d:\jiedan\7.5 AI配音\temp\video_processing\progress_ce8df491e76943479b886f662f880b64.txt -i D:/jiedan/7.5 AI配音/data\test\loop.mp4 -c:v h264_nvenc -c:a aac -b:a 128k -ar 44100 -ac 2 -avoid_negative_ts make_zero -fflags +genpts+igndts -vsync cfr -async 1 -max_muxing_queue_size 1024 -movflags +faststart -rc constqp -qp 25 -preset medium -g 30 -bf 2 -refs 3 -preset p1 -tune ull -rc cbr -g 60 -bf 0 -refs 1 d:\jiedan\7.5 AI配音\temp\video_processing\encoded_base_1b2b2334c068451c92ea6b152f5d6520.mp4
错误信息:

================================================================================

================================================================================
时间: 2025-08-07 02:15:23
操作: 基础视频编码
返回码: 15
命令: d:\jiedan\7.5 AI配音\ffmpeg\bin\ffmpeg.exe -y -progress d:\jiedan\7.5 AI配音\temp\video_processing\progress_26bd87791a2d4966a78d8b49094a610c.txt -i D:/jiedan/7.5 AI配音/data\test\loop.mp4 -c:v h264_nvenc -c:a aac -b:a 128k -ar 44100 -ac 2 -avoid_negative_ts make_zero -fflags +genpts+igndts -vsync cfr -async 1 -max_muxing_queue_size 1024 -movflags +faststart -preset p1 -tune ull -rc cbr -g 60 -bf 0 -refs 1 d:\jiedan\7.5 AI配音\temp\video_processing\encoded_base_ed0428d3903c43b8858e1310909e58a8.mp4
错误信息:

================================================================================

================================================================================
时间: 2025-08-07 02:47:10
操作: 基础视频编码
返回码: 15
命令: d:\jiedan\7.5 AI配音\ffmpeg\bin\ffmpeg.exe -y -progress d:\jiedan\7.5 AI配音\temp\video_processing\progress_d5a37fe0dad7468ea974949e8dfee885.txt -i D:/jiedan/7.5 AI配音/data\test\loop.mp4 -c:v h264_nvenc -c:a aac -b:a 128k -ar 44100 -ac 2 -avoid_negative_ts make_zero -fflags +genpts+igndts -vsync cfr -async 1 -max_muxing_queue_size 1024 -movflags +faststart -preset p1 -tune ull -rc cbr -g 60 -bf 0 -refs 1 d:\jiedan\7.5 AI配音\temp\video_processing\encoded_base_64b3cfb25d494a92bdb3eefe6a40293a.mp4
错误信息:

================================================================================

================================================================================
时间: 2025-08-07 02:49:58
操作: 预编码
问题: 进度监控超时
超时时长: 120.1秒
最后进度: 预编码: 9.4% (速度: 48.4x)
================================================================================

================================================================================
时间: 2025-08-07 12:40:38
操作: 快速循环
问题: 进度监控超时
超时时长: 120.5秒
最后进度: 快速循环: 22.5% (速度: 2310.0x) (预计剩余: 00:10)
================================================================================

================================================================================
时间: 2025-08-07 13:43:27
操作: 快速循环
问题: 进度监控超时
超时时长: 120.2秒
最后进度: 快速循环: 17.6% (速度: 1820.0x)
================================================================================

================================================================================
时间: 2025-08-07 14:48:08
操作: 预编码
错误: 总体超时（30分钟）
详细信息: 进度文件: d:\jiedan\7.5 AI配音\temp\video_processing\progress_5affce8e943f4d909c3637f046af4e6f.txt
================================================================================

================================================================================
时间: 2025-08-07 14:48:33
操作: 快速循环
错误: 总体超时（30分钟）
详细信息: 进度文件: d:\jiedan\7.5 AI配音\temp\video_processing\progress_4f707537ca504aaaa69a971a30165eda.txt
================================================================================

================================================================================
时间: 2025-08-07 18:59:31
操作: 基础视频编码
返回码: 15
命令: d:\jiedan\7.5 AI配音\ffmpeg\bin\ffmpeg.exe -y -progress d:\jiedan\7.5 AI配音\temp\video_processing\progress_7991b4385cec45768fbd43362dd41bfb.txt -i D:/jiedan/7.5 AI配音/data\test\loop.mp4 -c:v h264_nvenc -c:a aac -b:a 128k -ar 44100 -ac 2 -avoid_negative_ts make_zero -fflags +genpts+igndts -vsync cfr -async 1 -max_muxing_queue_size 1024 -movflags +faststart -preset p1 -tune ull -rc cbr -g 60 -bf 0 -refs 1 d:\jiedan\7.5 AI配音\temp\video_processing\encoded_base_58e4e6ab564e4d31956c07910d4a91b8.mp4
错误信息:

================================================================================

================================================================================
时间: 2025-08-09 11:34:03
操作: 快速循环
错误: 总体超时（30分钟）
详细信息: 进度文件: d:\jiedan\7.5 AI配音\temp\video_processing\progress_92b0931f5eb54317b75112b26d4eb283.txt
================================================================================

================================================================================
时间: 2025-08-09 13:17:09
操作: 预编码
错误: 总体超时（30分钟）
详细信息: 进度文件: d:\jiedan\7.5 AI配音\temp\video_processing\progress_2b20922fb59f43b2bccf1484c4266772.txt
================================================================================

================================================================================
时间: 2025-08-09 13:17:33
操作: 快速循环
错误: 总体超时（30分钟）
详细信息: 进度文件: d:\jiedan\7.5 AI配音\temp\video_processing\progress_33cded35c8994320a127dbab6c690ef6.txt
================================================================================

================================================================================
时间: 2025-08-09 20:42:34
操作: 添加字幕
错误: 总体超时（30分钟）
详细信息: 进度文件: d:\jiedan\7.5 AI配音\temp\video_processing\progress_23de8e107c7d4f039d0f6688d057a6cd.txt
================================================================================

================================================================================
时间: 2025-08-10 22:20:19
操作: 编码基础视频
错误: local variable 'monitor_state' referenced before assignment
详细信息: 视频路径: D:/jiedan/7.5 AI配音/data\test\loop.mp4
================================================================================

================================================================================
时间: 2025-08-10 22:20:20
操作: 编码基础视频
错误: local variable 'monitor_state' referenced before assignment
详细信息: 视频路径: D:/jiedan/7.5 AI配音/data\test\loop.mp4
================================================================================
