"""
AI配音主界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import re
import threading
import time
import datetime
from .core import VoiceTTSProcessor
from .config import config_manager
from .advanced_settings import AdvancedSettingsDialog

# 添加common模块路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from common.logger import logger

class VoiceTTSWindow:
    def __init__(self, parent):
        self.parent = parent
        self.processor = VoiceTTSProcessor()

        # 设置颜色主题
        self.bg_color = "#f8f9fa"
        self.panel_color = "#ffffff"
        self.text_color = "#212529"
        self.accent_color = "#4361ee"
        self.button_text_color = "#ffffff"

        # 创建主框架
        self.frame = tk.Frame(parent, bg=self.bg_color, padx=15, pady=10)
        self.frame.pack(fill=tk.BOTH, expand=True)

        # 文件列表和选择状态
        self.file_paths = []
        self.file_vars = []

        # 输出目录
        self.output_dir = ""

        # 处理状态
        self.is_processing = False
        self.is_paused = False
        self.processing_thread = None
        self.start_time = None
        self.timer_running = False

        # 超时监控
        self.last_progress_time = None
        self.timeout_monitor_thread = None
        self.timeout_threshold = 1200  # 1200秒(20分钟)超时阈值，兼容差电脑和网络慢的情况
        self.last_progress_content = ""  # 记录最后一次进度内容，检测是否真的有进展
        self.progress_update_count = 0  # 进度更新计数器

        # 创建界面
        self.create_top_section()
        self.create_main_section()
        self.create_bottom_section()

        # 加载保存的设置
        self.load_settings()

        # 异步加载语音列表
        self.load_voices_async()

    def create_top_section(self):
        """创建顶部区域，包含标题和文件导入按钮"""
        top_frame = tk.Frame(self.frame, bg=self.bg_color)
        top_frame.pack(fill=tk.X, pady=10)

        # 标题
        title_label = tk.Label(
            top_frame,
            text="AI配音",
            font=("微软雅黑", 16, "bold"),
            fg=self.text_color,
            bg=self.bg_color
        )
        title_label.pack(side=tk.LEFT, padx=10)

        # 导入文件按钮
        self.import_files_btn = tk.Button(
            top_frame,
            text="导入文件",
            font=("微软雅黑", 10),
            bg=self.accent_color,
            fg=self.button_text_color,
            padx=15,
            pady=5,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.import_files
        )
        self.import_files_btn.pack(side=tk.RIGHT, padx=5)

        # 导入文件夹按钮
        self.import_folder_btn = tk.Button(
            top_frame,
            text="导入文件夹",
            font=("微软雅黑", 10),
            bg=self.accent_color,
            fg=self.button_text_color,
            padx=15,
            pady=5,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.import_folder
        )
        self.import_folder_btn.pack(side=tk.RIGHT, padx=5)

    def create_main_section(self):
        """创建主要内容区域"""
        main_frame = tk.Frame(self.frame, bg=self.bg_color)
        main_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # 左侧：文件列表
        left_frame = tk.Frame(main_frame, bg=self.bg_color)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        self.create_file_list_section(left_frame)

        # 右侧：配音设置
        right_frame = tk.Frame(main_frame, bg=self.bg_color)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))

        self.create_settings_section(right_frame)

    def create_file_list_section(self, parent):
        """创建文件列表区域"""
        # 文件列表标题
        list_frame = tk.LabelFrame(
            parent,
            text="文件列表",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color,
            padx=10,
            pady=10
        )
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 工具栏
        toolbar_frame = tk.Frame(list_frame, bg=self.panel_color)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # 全选按钮
        self.select_all_btn = tk.Button(
            toolbar_frame,
            text="全选",
            font=("微软雅黑", 9),
            bg="#28a745",
            fg="white",
            padx=10,
            pady=2,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.select_all_files
        )
        self.select_all_btn.pack(side=tk.LEFT, padx=2)

        # 全不选按钮
        self.deselect_all_btn = tk.Button(
            toolbar_frame,
            text="全不选",
            font=("微软雅黑", 9),
            bg="#dc3545",
            fg="white",
            padx=10,
            pady=2,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.deselect_all_files
        )
        self.deselect_all_btn.pack(side=tk.LEFT, padx=2)

        # 清空列表按钮
        self.clear_list_btn = tk.Button(
            toolbar_frame,
            text="清空列表",
            font=("微软雅黑", 9),
            bg="#6c757d",
            fg="white",
            padx=10,
            pady=2,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.clear_file_list
        )
        self.clear_list_btn.pack(side=tk.LEFT, padx=2)

        # 重新排序按钮
        self.resort_btn = tk.Button(
            toolbar_frame,
            text="重新排序",
            font=("微软雅黑", 9),
            bg="#17a2b8",
            fg="white",
            padx=10,
            pady=2,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.resort_file_list
        )
        self.resort_btn.pack(side=tk.LEFT, padx=2)



        # 文件列表容器
        list_container = tk.Frame(list_frame, bg=self.panel_color)
        list_container.pack(fill=tk.BOTH, expand=True)

        # 创建滚动条
        scrollbar = tk.Scrollbar(list_container)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 创建文件列表框架（用于放置复选框）
        self.file_list_canvas = tk.Canvas(
            list_container,
            bg="white",
            yscrollcommand=scrollbar.set,
            highlightthickness=0
        )
        self.file_list_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar.config(command=self.file_list_canvas.yview)

        # 创建内部框架
        self.file_list_frame = tk.Frame(self.file_list_canvas, bg="white")
        self.file_list_canvas.create_window((0, 0), window=self.file_list_frame, anchor="nw")

        # 绑定鼠标滚轮事件
        self.file_list_canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.file_list_frame.bind("<Configure>", self._on_frame_configure)
        # 可选：启用 tkinterdnd2 的拖拽（如果可用）
        try:
            from tkinterdnd2 import DND_FILES
            # 仅在该控件上注册拖放，避免影响全局输入
            self.file_list_canvas.drop_target_register(DND_FILES)
            self.file_list_canvas.dnd_bind('<<Drop>>', self._on_drop_files)
            print("✅ AI配音模块：tkinterdnd2拖拽功能已启用")
        except Exception as e:
            print(f"⚠️ AI配音模块：tkinterdnd2拖拽功能启用失败: {e}")


    def create_settings_section(self, parent):
        """创建配音设置区域"""
        settings_frame = tk.LabelFrame(
            parent,
            text="配音设置",
            font=("微软雅黑", 10),
            fg=self.text_color,
            bg=self.panel_color,
            padx=10,
            pady=10
        )
        settings_frame.pack(fill=tk.BOTH, expand=True)

        # 顶部按钮区域
        top_buttons_frame = tk.Frame(settings_frame, bg=self.panel_color)
        top_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        # 高级设置按钮
        self.advanced_btn = tk.Button(
            top_buttons_frame,
            text="高级设置",
            font=("微软雅黑", 9),
            bg="#17a2b8",
            fg="white",
            padx=12,
            pady=5,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.open_advanced_settings
        )
        self.advanced_btn.pack(side=tk.RIGHT)

        # 路径设置区域（紧凑布局）
        paths_frame = tk.LabelFrame(
            settings_frame,
            text="路径设置",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color,
            padx=8,
            pady=5
        )
        paths_frame.pack(fill=tk.X, pady=(0, 8))

        # FFmpeg路径设置（紧凑）
        ffmpeg_frame = tk.Frame(paths_frame, bg=self.panel_color)
        ffmpeg_frame.pack(fill=tk.X, pady=2)

        ffmpeg_label_frame = tk.Frame(ffmpeg_frame, bg=self.panel_color)
        ffmpeg_label_frame.pack(fill=tk.X)

        tk.Label(
            ffmpeg_label_frame,
            text="FFmpeg:",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color,
            width=8,
            anchor="w"
        ).pack(side=tk.LEFT)

        self.ffmpeg_path_var = tk.StringVar()
        self.ffmpeg_path_entry = tk.Entry(
            ffmpeg_label_frame,
            textvariable=self.ffmpeg_path_var,
            font=("微软雅黑", 8),
            state="readonly"
        )
        self.ffmpeg_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))

        self.select_ffmpeg_btn = tk.Button(
            ffmpeg_label_frame,
            text="选择",
            font=("微软雅黑", 8),
            bg=self.accent_color,
            fg="white",
            padx=8,
            pady=2,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.select_ffmpeg_path
        )
        self.select_ffmpeg_btn.pack(side=tk.RIGHT)

        # FFmpeg状态标签（紧凑）
        self.ffmpeg_status_label = tk.Label(
            ffmpeg_frame,
            text="未设置FFmpeg路径",
            font=("微软雅黑", 7),
            fg="#6c757d",
            bg=self.panel_color
        )
        self.ffmpeg_status_label.pack(anchor=tk.W, pady=(1, 0), padx=(65, 0))

        # 输出目录设置（紧凑）
        output_frame = tk.Frame(paths_frame, bg=self.panel_color)
        output_frame.pack(fill=tk.X, pady=2)

        output_label_frame = tk.Frame(output_frame, bg=self.panel_color)
        output_label_frame.pack(fill=tk.X)

        tk.Label(
            output_label_frame,
            text="输出目录:",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color,
            width=8,
            anchor="w"
        ).pack(side=tk.LEFT)

        self.output_dir_var = tk.StringVar()
        self.output_dir_entry = tk.Entry(
            output_label_frame,
            textvariable=self.output_dir_var,
            font=("微软雅黑", 8),
            state="readonly"
        )
        self.output_dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))

        self.select_dir_btn = tk.Button(
            output_label_frame,
            text="选择",
            font=("微软雅黑", 8),
            bg=self.accent_color,
            fg="white",
            padx=8,
            pady=2,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.select_output_dir
        )
        self.select_dir_btn.pack(side=tk.RIGHT)

        # 语音设置区域（紧凑布局）
        voice_frame = tk.LabelFrame(
            settings_frame,
            text="语音设置",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color,
            padx=8,
            pady=5
        )
        voice_frame.pack(fill=tk.X, pady=(0, 8))

        # 语音选择
        voice_select_frame = tk.Frame(voice_frame, bg=self.panel_color)
        voice_select_frame.pack(fill=tk.X, pady=2)

        tk.Label(
            voice_select_frame,
            text="语音:",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color,
            width=8,
            anchor="w"
        ).pack(side=tk.LEFT)

        self.voice_var = tk.StringVar(value="zh-CN-XiaoxiaoNeural")
        self.voice_combo = ttk.Combobox(
            voice_select_frame,
            textvariable=self.voice_var,
            values=[],  # 初始为空，稍后加载
            state="readonly",
            font=("微软雅黑", 8)
        )
        self.voice_combo.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # 语音加载状态标签（紧凑）
        self.voice_status_label = tk.Label(
            voice_frame,
            text="正在加载语音列表...",
            font=("微软雅黑", 7),
            fg="#6c757d",
            bg=self.panel_color
        )
        self.voice_status_label.pack(anchor=tk.W, pady=(1, 0), padx=(65, 0))

        # 音频参数设置区域（紧凑布局）
        audio_frame = tk.LabelFrame(
            settings_frame,
            text="音频参数",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color,
            padx=8,
            pady=5
        )
        audio_frame.pack(fill=tk.X, pady=(0, 8))

        # 语速设置（紧凑）
        rate_frame = tk.Frame(audio_frame, bg=self.panel_color)
        rate_frame.pack(fill=tk.X, pady=2)

        rate_label_frame = tk.Frame(rate_frame, bg=self.panel_color)
        rate_label_frame.pack(fill=tk.X)

        tk.Label(
            rate_label_frame,
            text="语速:",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color,
            width=8,
            anchor="w"
        ).pack(side=tk.LEFT)

        # 语速输入框
        self.rate_entry_var = tk.StringVar(value="0")
        rate_entry = tk.Entry(
            rate_label_frame,
            textvariable=self.rate_entry_var,
            font=("微软雅黑", 8),
            width=5,
            justify=tk.CENTER
        )
        rate_entry.pack(side=tk.RIGHT, padx=(2, 0))
        rate_entry.bind('<Return>', self.on_rate_entry_change)
        rate_entry.bind('<FocusOut>', self.on_rate_entry_change)
        rate_entry.bind('<KeyRelease>', self.on_rate_entry_realtime)

        tk.Label(
            rate_label_frame,
            text="%",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color
        ).pack(side=tk.RIGHT)

        self.rate_var = tk.IntVar(value=0)
        self.rate_scale = tk.Scale(
            rate_frame,
            from_=-50,
            to=50,
            orient=tk.HORIZONTAL,
            variable=self.rate_var,
            font=("微软雅黑", 7),
            bg=self.panel_color,
            fg=self.text_color,
            highlightthickness=0,
            length=150,
            command=self.update_rate_from_scale
        )
        self.rate_scale.pack(fill=tk.X, pady=(1, 0), padx=(65, 0))

        # 音调设置（紧凑）
        pitch_frame = tk.Frame(audio_frame, bg=self.panel_color)
        pitch_frame.pack(fill=tk.X, pady=2)

        pitch_label_frame = tk.Frame(pitch_frame, bg=self.panel_color)
        pitch_label_frame.pack(fill=tk.X)

        tk.Label(
            pitch_label_frame,
            text="音调:",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color,
            width=8,
            anchor="w"
        ).pack(side=tk.LEFT)

        # 音调输入框
        self.pitch_entry_var = tk.StringVar(value="0")
        pitch_entry = tk.Entry(
            pitch_label_frame,
            textvariable=self.pitch_entry_var,
            font=("微软雅黑", 8),
            width=5,
            justify=tk.CENTER
        )
        pitch_entry.pack(side=tk.RIGHT, padx=(2, 0))
        pitch_entry.bind('<Return>', self.on_pitch_entry_change)
        pitch_entry.bind('<FocusOut>', self.on_pitch_entry_change)
        pitch_entry.bind('<KeyRelease>', self.on_pitch_entry_realtime)

        self.pitch_var = tk.IntVar(value=0)
        self.pitch_scale = tk.Scale(
            pitch_frame,
            from_=-10,
            to=10,
            orient=tk.HORIZONTAL,
            variable=self.pitch_var,
            font=("微软雅黑", 7),
            bg=self.panel_color,
            fg=self.text_color,
            highlightthickness=0,
            length=150,
            command=self.update_pitch_from_scale
        )
        self.pitch_scale.pack(fill=tk.X, pady=(1, 0), padx=(65, 0))

        # 音量设置（紧凑）
        volume_frame = tk.Frame(audio_frame, bg=self.panel_color)
        volume_frame.pack(fill=tk.X, pady=2)

        volume_label_frame = tk.Frame(volume_frame, bg=self.panel_color)
        volume_label_frame.pack(fill=tk.X)

        tk.Label(
            volume_label_frame,
            text="音量(可超100%):",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color,
            width=12,
            anchor="w"
        ).pack(side=tk.LEFT)

        # 音量输入框
        self.volume_entry_var = tk.StringVar(value="50")
        volume_entry = tk.Entry(
            volume_label_frame,
            textvariable=self.volume_entry_var,
            font=("微软雅黑", 8),
            width=5,
            justify=tk.CENTER
        )
        volume_entry.pack(side=tk.RIGHT, padx=(2, 0))
        volume_entry.bind('<Return>', self.on_volume_entry_change)
        volume_entry.bind('<FocusOut>', self.on_volume_entry_change)
        volume_entry.bind('<KeyRelease>', self.on_volume_entry_realtime)

        self.volume_var = tk.IntVar(value=50)
        self.volume_scale = tk.Scale(
            volume_frame,
            from_=0,
            to=300,  # 扩展到300%，允许更大音量提高
            orient=tk.HORIZONTAL,
            variable=self.volume_var,
            font=("微软雅黑", 7),
            bg=self.panel_color,
            fg=self.text_color,
            highlightthickness=0,
            length=150,
            command=self.update_volume_from_scale
        )
        self.volume_scale.pack(fill=tk.X, pady=(1, 0), padx=(65, 0))



        # 其他选项区域（紧凑布局）
        options_frame = tk.LabelFrame(
            settings_frame,
            text="其他选项",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color,
            padx=8,
            pady=5
        )
        options_frame.pack(fill=tk.X, pady=(0, 8))

        # 字幕生成选项（紧凑）
        subtitle_frame = tk.Frame(options_frame, bg=self.panel_color)
        subtitle_frame.pack(fill=tk.X, pady=2)

        self.generate_srt_var = tk.BooleanVar(value=True)
        self.generate_srt_check = tk.Checkbutton(
            subtitle_frame,
            text="生成字幕文件 (.srt)",
            variable=self.generate_srt_var,
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color
        )
        self.generate_srt_check.pack(anchor=tk.W)

        # 字幕说明（紧凑）
        subtitle_desc = tk.Label(
            subtitle_frame,
            text="字幕由edge-tts自动生成，时间轴精确匹配语音",
            font=("微软雅黑", 7),
            fg="#6c757d",
            bg=self.panel_color
        )
        subtitle_desc.pack(anchor=tk.W, pady=(1, 0), padx=(20, 0))

        # 按钮区域（紧凑布局）
        button_frame = tk.Frame(settings_frame, bg=self.panel_color)
        button_frame.pack(fill=tk.X, pady=8)

        # 第一行按钮
        button_row1 = tk.Frame(button_frame, bg=self.panel_color)
        button_row1.pack(fill=tk.X, pady=(0, 5))

        # 开始处理按钮
        self.process_btn = tk.Button(
            button_row1,
            text="开始配音",
            font=("微软雅黑", 10, "bold"),
            bg="#28a745",
            fg="white",
            padx=15,
            pady=8,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.start_processing
        )
        self.process_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        # 暂停按钮
        self.pause_btn = tk.Button(
            button_row1,
            text="暂停",
            font=("微软雅黑", 9),
            bg="#ffc107",
            fg="black",
            padx=12,
            pady=8,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.pause_processing,
            state=tk.DISABLED
        )
        self.pause_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 第二行按钮
        button_row2 = tk.Frame(button_frame, bg=self.panel_color)
        button_row2.pack(fill=tk.X)

        # 停止按钮
        self.stop_btn = tk.Button(
            button_row2,
            text="停止处理",
            font=("微软雅黑", 9),
            bg="#dc3545",
            fg="white",
            padx=12,
            pady=6,
            relief=tk.FLAT,
            cursor="hand2",
            command=self.stop_processing,
            state=tk.DISABLED
        )
        self.stop_btn.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 进度显示区域（重新设计）
        progress_frame = tk.LabelFrame(
            settings_frame,
            text="处理进度",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg=self.panel_color,
            padx=12,
            pady=8
        )
        progress_frame.pack(fill=tk.X, pady=(8, 0))

        # 使用Grid布局来更好地控制组件位置
        progress_frame.grid_columnconfigure(1, weight=1)  # 让进度条列可以扩展

        # 第一行：总体进度
        tk.Label(
            progress_frame,
            text="总体:",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color,
            width=8,
            anchor="w"
        ).grid(row=0, column=0, sticky="w", padx=(0, 5), pady=2)

        self.overall_progress = ttk.Progressbar(
            progress_frame,
            mode='determinate'
        )
        self.overall_progress.grid(row=0, column=1, sticky="ew", padx=(0, 8), pady=2)

        self.overall_label = tk.Label(
            progress_frame,
            text="0/0 (0%)",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color,
            width=12,
            anchor="w"
        )
        self.overall_label.grid(row=0, column=2, sticky="w", pady=2)

        # 第二行：分段进度
        tk.Label(
            progress_frame,
            text="分段:",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color,
            width=8,
            anchor="w"
        ).grid(row=1, column=0, sticky="w", padx=(0, 5), pady=2)

        self.segment_progress = ttk.Progressbar(
            progress_frame,
            mode='determinate'
        )
        self.segment_progress.grid(row=1, column=1, sticky="ew", padx=(0, 8), pady=2)

        self.segment_label = tk.Label(
            progress_frame,
            text="0/0",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color,
            width=12,
            anchor="w"
        )
        self.segment_label.grid(row=1, column=2, sticky="w", pady=2)

        # 第三行：当前文件和计时器
        tk.Label(
            progress_frame,
            text="文件:",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color,
            width=8,
            anchor="w"
        ).grid(row=2, column=0, sticky="w", padx=(0, 5), pady=2)

        # 文件名和计时器的容器
        file_timer_frame = tk.Frame(progress_frame, bg=self.panel_color)
        file_timer_frame.grid(row=2, column=1, columnspan=2, sticky="ew", pady=2)
        file_timer_frame.grid_columnconfigure(0, weight=1)

        self.current_file_label = tk.Label(
            file_timer_frame,
            text="无",
            font=("微软雅黑", 8),
            fg=self.accent_color,
            bg=self.panel_color,
            anchor="w"
        )
        self.current_file_label.grid(row=0, column=0, sticky="ew", padx=(0, 10))

        # 计时器标签和时间
        timer_container = tk.Frame(file_timer_frame, bg=self.panel_color)
        timer_container.grid(row=0, column=1, sticky="e")

        tk.Label(
            timer_container,
            text="用时:",
            font=("微软雅黑", 8),
            fg=self.text_color,
            bg=self.panel_color
        ).pack(side=tk.LEFT)

        self.timer_label = tk.Label(
            timer_container,
            text="00:00:00",
            font=("微软雅黑", 8, "bold"),
            fg="#007bff",
            bg=self.panel_color
        )
        self.timer_label.pack(side=tk.LEFT, padx=(5, 0))

    def create_bottom_section(self):
        """创建底部状态栏"""
        bottom_frame = tk.Frame(self.frame, bg="#e9ecef", height=30)
        bottom_frame.pack(fill=tk.X, pady=5)

        # 左侧状态标签
        self.status_label = tk.Label(
            bottom_frame,
            text="就绪",
            font=("微软雅黑", 9),
            fg=self.text_color,
            bg="#e9ecef",
            anchor="w",
            padx=10,
            pady=5
        )
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 右侧按钮区域
        button_frame = tk.Frame(bottom_frame, bg="#e9ecef")
        button_frame.pack(side=tk.RIGHT, padx=10)

        # 查看错误日志按钮
        self.error_log_btn = tk.Button(
            button_frame,
            text="查看错误日志",
            font=("微软雅黑", 8),
            bg="#dc3545",
            fg="white",
            padx=8,
            pady=2,
            cursor="hand2",
            command=self.show_error_log
        )
        self.error_log_btn.pack(side=tk.RIGHT, padx=(0, 5))

        # 清空日志按钮
        self.clear_log_btn = tk.Button(
            button_frame,
            text="清空日志",
            font=("微软雅黑", 8),
            bg="#6c757d",
            fg="white",
            padx=8,
            pady=2,
            cursor="hand2",
            command=self.clear_error_log
        )
        self.clear_log_btn.pack(side=tk.RIGHT, padx=(0, 5))

    def show_error_log(self):
        """显示错误日志"""
        try:
            log_path = logger.get_log_path()

            if not os.path.exists(log_path):
                messagebox.showinfo("提示", "暂无错误日志文件")
                return

            # 读取日志内容
            log_content = self._read_file_with_encoding_detection(log_path)

            if not log_content.strip():
                messagebox.showinfo("提示", "错误日志文件为空")
                return

            # 创建日志查看窗口
            log_window = tk.Toplevel(self.parent)
            log_window.title("错误日志")
            log_window.geometry("800x600")
            log_window.transient(self.parent)

            # 创建文本框和滚动条
            text_frame = tk.Frame(log_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 文本框
            text_widget = tk.Text(
                text_frame,
                wrap=tk.WORD,
                font=("Consolas", 12),
                bg="#f8f9fa",
                fg="#212529"
            )

            # 滚动条
            scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            # 布局
            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 插入日志内容
            text_widget.insert(tk.END, log_content)
            text_widget.config(state=tk.DISABLED)

            # 滚动到底部
            text_widget.see(tk.END)

            # 按钮框架
            button_frame = tk.Frame(log_window)
            button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

            # 刷新按钮
            refresh_btn = tk.Button(
                button_frame,
                text="刷新",
                font=("微软雅黑", 9),
                bg="#28a745",
                fg="white",
                padx=15,
                pady=5,
                cursor="hand2",
                command=lambda: self.refresh_log_content(text_widget, log_path)
            )
            refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

            # 复制到剪贴板按钮
            copy_btn = tk.Button(
                button_frame,
                text="复制到剪贴板",
                font=("微软雅黑", 9),
                bg="#17a2b8",
                fg="white",
                padx=15,
                pady=5,
                cursor="hand2",
                command=lambda: self.copy_log_to_clipboard(log_content, log_window)
            )
            copy_btn.pack(side=tk.LEFT, padx=(0, 10))

            # 关闭按钮
            close_btn = tk.Button(
                button_frame,
                text="关闭",
                font=("微软雅黑", 9),
                bg="#6c757d",
                fg="white",
                padx=15,
                pady=5,
                cursor="hand2",
                command=log_window.destroy
            )
            close_btn.pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("错误", f"无法打开错误日志: {str(e)}")

    def refresh_log_content(self, text_widget, log_path):
        """刷新日志内容"""
        try:
            log_content = self._read_file_with_encoding_detection(log_path)

            text_widget.config(state=tk.NORMAL)
            text_widget.delete(1.0, tk.END)
            text_widget.insert(tk.END, log_content)
            text_widget.config(state=tk.DISABLED)
            text_widget.see(tk.END)

        except Exception as e:
            messagebox.showerror("错误", f"刷新日志失败: {str(e)}")

    def copy_log_to_clipboard(self, log_content, parent_window):
        """复制日志到剪贴板"""
        try:
            parent_window.clipboard_clear()
            parent_window.clipboard_append(log_content)
            messagebox.showinfo("成功", "日志内容已复制到剪贴板", parent=parent_window)
        except Exception as e:
            messagebox.showerror("错误", f"复制失败: {str(e)}", parent=parent_window)

    def clear_error_log(self):
        """清空错误日志"""
        try:
            result = messagebox.askyesno(
                "确认",
                "确定要清空错误日志吗？\n此操作不可撤销。",
                parent=self.parent
            )

            if result:
                logger.clear_log()
                messagebox.showinfo("成功", "错误日志已清空", parent=self.parent)

        except Exception as e:
            messagebox.showerror("错误", f"清空日志失败: {str(e)}", parent=self.parent)

    def load_settings(self):
        """加载保存的设置"""
        try:
            # 加载FFmpeg路径
            ffmpeg_path = config_manager.get_ffmpeg_path()
            if ffmpeg_path:
                self.ffmpeg_path_var.set(ffmpeg_path)
                if self.processor.is_ffmpeg_available():
                    self.ffmpeg_status_label.config(
                        text="FFmpeg已设置，将使用高质量音频合并",
                        fg="#28a745"
                    )
                else:
                    self.ffmpeg_status_label.config(
                        text="FFmpeg路径无效，将使用简单合并方式",
                        fg="#dc3545"
                    )

            # 加载输出目录
            last_output_dir = config_manager.get_last_output_dir()
            if last_output_dir and os.path.exists(last_output_dir):
                self.output_dir = last_output_dir
                self.output_dir_var.set(last_output_dir)

            # 加载语音设置
            voice_settings = config_manager.get_voice_settings()
            self.voice_var.set(voice_settings.get("voice", "zh-CN-XiaoxiaoNeural"))
            self.rate_var.set(voice_settings.get("rate", 0))
            self.pitch_var.set(voice_settings.get("pitch", 0))
            self.volume_var.set(voice_settings.get("volume", 50))

            # 更新输入框显示
            self.rate_entry_var.set(str(self.rate_var.get()))
            self.pitch_entry_var.set(str(self.pitch_var.get()))
            self.volume_entry_var.set(str(self.volume_var.get()))

            # 加载字幕生成设置
            self.generate_srt_var.set(config_manager.get_generate_srt())

        except Exception as e:
            print(f"加载设置失败: {e}")

    def save_current_settings(self):
        """保存当前设置"""
        try:
            # 保存语音设置
            config_manager.set_voice_settings(
                self.get_selected_voice_id(),
                self.rate_var.get(),
                self.pitch_var.get(),
                self.volume_var.get()
            )

            # 保存字幕生成设置
            config_manager.set_generate_srt(self.generate_srt_var.get())

            # 保存输出目录
            if self.output_dir:
                config_manager.set_last_output_dir(self.output_dir)

        except Exception as e:
            print(f"保存设置失败: {e}")

    def load_voices_async(self):
        """异步加载语音列表"""
        def load_voices_thread():
            try:
                # 在后台线程中加载语音列表
                voices = self.processor.load_voices()

                # 在主线程中更新UI
                self.voice_combo.after(0, self.update_voice_list, voices)

            except Exception as e:
                print(f"加载语音列表失败: {e}")
                # 更新状态标签
                self.voice_status_label.after(0,
                    lambda: self.voice_status_label.config(
                        text="语音列表加载失败，使用默认语音",
                        fg="#dc3545"
                    )
                )

        # 启动后台线程
        thread = threading.Thread(target=load_voices_thread, daemon=True)
        thread.start()

    def update_voice_list(self, voices):
        """更新语音列表UI"""
        try:
            # 构建显示用的语音列表
            voice_items = []
            for voice_id, display_name in voices.items():
                voice_items.append(f"{voice_id} - {display_name}")

            # 更新下拉框
            self.voice_combo['values'] = voice_items

            # 设置默认选择
            if voice_items:
                # 尝试恢复之前保存的语音设置
                saved_voice = config_manager.get_voice_settings().get("voice", "zh-CN-XiaoxiaoNeural")

                # 查找匹配的语音项
                selected_item = None
                for item in voice_items:
                    if item.startswith(saved_voice):
                        selected_item = item
                        break

                if selected_item:
                    self.voice_combo.set(selected_item)
                    self.voice_var.set(saved_voice)  # 保持原始ID
                else:
                    # 如果没找到，使用第一个
                    self.voice_combo.set(voice_items[0])
                    first_voice_id = voice_items[0].split(' - ')[0]
                    self.voice_var.set(first_voice_id)

            # 更新状态标签
            self.voice_status_label.config(
                text=f"已加载 {len(voices)} 个中文语音",
                fg="#28a745"
            )

        except Exception as e:
            print(f"更新语音列表UI失败: {e}")
            self.voice_status_label.config(
                text="语音列表更新失败",
                fg="#dc3545"
            )

    def get_selected_voice_id(self):
        """获取当前选择的语音ID"""
        selected_item = self.voice_combo.get()
        if selected_item and ' - ' in selected_item:
            return selected_item.split(' - ')[0]
        return self.voice_var.get()



    def start_timer(self):
        """启动计时器"""
        self.start_time = time.time()
        self.timer_running = True
        self.update_timer()

    def stop_timer(self):
        """停止计时器"""
        self.timer_running = False

    def update_timer(self):
        """更新计时器显示"""
        if self.timer_running and self.start_time:
            elapsed = time.time() - self.start_time
            hours = int(elapsed // 3600)
            minutes = int((elapsed % 3600) // 60)
            seconds = int(elapsed % 60)

            time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            self.timer_label.config(text=time_str)

            # 每秒更新一次
            self.timer_label.after(1000, self.update_timer)

    def update_progress(self, progress_info):
        """更新进度显示"""
        # 更新超时监控时间和计数器
        current_time = time.time()
        self.last_progress_time = current_time
        self.progress_update_count += 1  # 增加进度更新计数

        # 检查进度内容是否有实际变化
        progress_text = str(progress_info)
        if progress_text != self.last_progress_content:
            self.last_progress_content = progress_text
            # 如果进度内容有变化，重置超时计时
            self.last_progress_time = current_time

        if isinstance(progress_info, dict):
            if progress_info.get("type") == "file_progress":
                # 更新文件进度
                current = progress_info.get("current_file", 0)
                total = progress_info.get("total_files", 0)
                file_name = progress_info.get("file_name", "")

                self.current_file_label.config(text=file_name)

            elif progress_info.get("type") == "overall_progress":
                # 更新总体进度
                completed = progress_info.get("completed", 0)
                total = progress_info.get("total", 0)
                percentage = progress_info.get("percentage", 0)

                self.overall_progress['value'] = percentage
                self.overall_label.config(text=f"{completed}/{total} ({percentage:.1f}%)")

            elif progress_info.get("type") == "segment_progress":
                # 更新分段进度（不显示分段文本）
                current = progress_info.get("current_segment", 0)
                total = progress_info.get("total_segments", 0)

                if total > 0:
                    percentage = (current / total) * 100
                    self.segment_progress['value'] = percentage
                    self.segment_label.config(text=f"{current}/{total}")
        else:
            # 过滤字符串消息，只显示重要的状态信息
            message = str(progress_info)

            # 定义需要显示的重要状态关键词
            important_keywords = [
                "就绪", "开始处理", "处理完成", "处理失败", "用户停止",
                "语音生成完成", "音频合并完成", "生成完成",
                "从断点继续", "保存断点", "错误", "失败", "超时",
                "正在合并", "导出", "生成字幕"
            ]

            # 定义需要过滤掉的详细信息关键词
            filter_keywords = [
                "执行命令", "正在处理第", "个分段", "个片段",
                "重试第", "秒后重试", "分段", "处理异常"
            ]

            # 检查是否包含需要过滤的关键词
            should_filter = any(keyword in message for keyword in filter_keywords)

            # 如果消息包含“完成/合并/FFmpeg”等关键字，则不应被过滤
            if any(k in message for k in ["完成", "合并", "FFmpeg", "处理完成"]):
                should_filter = False

            # 检查是否包含重要状态关键词
            is_important = any(keyword in message for keyword in important_keywords)

            # 只显示重要状态信息，过滤掉详细的分段处理信息
            if is_important or not should_filter:
                self.status_label.config(text=message)

    def reset_progress(self):
        """重置进度显示"""
        self.overall_progress['value'] = 0
        self.overall_label.config(text="0/0 (0%)")
        self.segment_progress['value'] = 0
        self.segment_label.config(text="0/0")
        self.current_file_label.config(text="无")
        self.timer_label.config(text="00:00:00")
        # 重置进度计数器和超时监控
        self.progress_update_count = 0
        self.last_progress_time = None

    def pause_processing(self):
        """暂停处理"""
        if self.is_processing and not self.is_paused:
            self.is_paused = True
            self.processor.pause_processing()

            # 更新按钮状态
            self.pause_btn.config(text="继续", bg="#28a745")
            self.status_label.config(text="处理已暂停")

        elif self.is_processing and self.is_paused:
            # 继续处理
            self.is_paused = False
            self.processor.resume_processing()

            # 更新按钮状态
            self.pause_btn.config(text="暂停", bg="#ffc107")
            self.status_label.config(text="继续处理中...")

    def stop_processing(self):
        """停止处理"""
        if self.is_processing:
            # 立即更新UI状态，避免卡死
            self.is_processing = False
            self.is_paused = False
            self.stop_timer()
            self.stop_timeout_monitor()
            self.status_label.config(text="正在停止处理...")

            # 禁用停止按钮，避免重复点击
            self.stop_btn.config(state=tk.DISABLED)

            # 在后台线程中执行清理操作，避免UI卡死
            import threading
            def cleanup_thread():
                try:
                    # 设置超时机制，避免无限等待
                    import signal
                    import time

                    def timeout_handler(signum, frame):
                        print("清理操作超时，强制完成")
                        raise TimeoutError("清理操作超时")

                    # 设置10秒超时
                    if hasattr(signal, 'SIGALRM'):  # Unix系统
                        signal.signal(signal.SIGALRM, timeout_handler)
                        signal.alarm(10)

                    try:
                        # 通知处理器停止（会自动终止FFmpeg进程）
                        self.processor.stop_processing()
                    finally:
                        # 取消超时
                        if hasattr(signal, 'SIGALRM'):
                            signal.alarm(0)

                    # 在主线程中更新UI
                    self.parent.after(0, self._finish_stop_processing)
                except Exception as e:
                    print(f"停止处理时出错: {e}")
                    # 即使出错也要更新UI
                    self.parent.after(0, self._finish_stop_processing)

            cleanup_thread = threading.Thread(target=cleanup_thread, daemon=True)
            cleanup_thread.start()

            # 添加备用超时机制，确保UI不会永久卡死
            def emergency_timeout():
                print("紧急超时机制触发，强制完成UI更新")
                self._finish_stop_processing()

            # 15秒后强制完成UI更新
            self.parent.after(15000, emergency_timeout)

    def _finish_stop_processing(self):
        """完成停止处理的UI更新"""
        self.status_label.config(text="处理已停止，FFmpeg进程已清理")

        # 重新启用按钮
        self.process_btn.config(state=tk.NORMAL)
        self.import_files_btn.config(state=tk.NORMAL)
        self.import_folder_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.pause_btn.config(state=tk.DISABLED, text="暂停", bg="#ffc107")

    def update_rate_from_scale(self, value):
        """从滑块更新语速"""
        self.rate_entry_var.set(str(value))

    def update_pitch_from_scale(self, value):
        """从滑块更新音调"""
        self.pitch_entry_var.set(str(value))

    def update_volume_from_scale(self, value):
        """从滑块更新音量"""
        self.volume_entry_var.set(str(value))

    def on_rate_entry_change(self, event=None):
        """处理语速输入框变化"""
        try:
            value = int(self.rate_entry_var.get())
            # 限制范围
            value = max(-50, min(50, value))
            self.rate_var.set(value)
            self.rate_entry_var.set(str(value))
        except ValueError:
            # 如果输入无效，恢复到当前滑块值
            self.rate_entry_var.set(str(self.rate_var.get()))

    def on_pitch_entry_change(self, event=None):
        """处理音调输入框变化"""
        try:
            value = int(self.pitch_entry_var.get())
            # 限制范围
            value = max(-10, min(10, value))
            self.pitch_var.set(value)
            self.pitch_entry_var.set(str(value))
        except ValueError:
            # 如果输入无效，恢复到当前滑块值
            self.pitch_entry_var.set(str(self.pitch_var.get()))

    def on_volume_entry_change(self, event=None):
        """处理音量输入框变化"""
        try:
            value = int(self.volume_entry_var.get())
            # 限制范围，允许到300%
            value = max(0, min(300, value))
            self.volume_var.set(value)
            self.volume_entry_var.set(str(value))
        except ValueError:
            # 如果输入无效，恢复到当前滑块值
            self.volume_entry_var.set(str(self.volume_var.get()))

    def on_rate_entry_realtime(self, event=None):
        """实时处理语速输入框变化"""
        try:
            text = self.rate_entry_var.get()
            if text and text != "-":  # 允许输入负号
                value = int(text)
                # 限制范围
                if -50 <= value <= 50:
                    self.rate_var.set(value)
        except ValueError:
            pass  # 忽略无效输入，不做任何操作

    def on_pitch_entry_realtime(self, event=None):
        """实时处理音调输入框变化"""
        try:
            text = self.pitch_entry_var.get()
            if text and text != "-":  # 允许输入负号
                value = int(text)
                # 限制范围
                if -10 <= value <= 10:
                    self.pitch_var.set(value)
        except ValueError:
            pass  # 忽略无效输入，不做任何操作

    def on_volume_entry_realtime(self, event=None):
        """实时处理音量输入框变化"""
        try:
            text = self.volume_entry_var.get()
            if text:
                value = int(text)
                # 限制范围，允许到300%
                if 0 <= value <= 300:
                    self.volume_var.set(value)
        except ValueError:
            pass  # 忽略无效输入，不做任何操作

    def _on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        self.file_list_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

    def _on_frame_configure(self, event):
        """处理框架配置事件"""
        # event参数由tkinter传入，虽然未直接使用但需要保留
        self.file_list_canvas.configure(scrollregion=self.file_list_canvas.bbox("all"))

    def natural_sort_key(self, filename):
        """
        生成自然排序的键，让数字按数值大小排序
        例如：file_1.txt, file_2.txt, file_10.txt 而不是 file_1.txt, file_10.txt, file_2.txt
        """
        def convert(text):
            return int(text) if text.isdigit() else text.lower()

        # 将文件名分割为数字和非数字部分
        return [convert(c) for c in re.split(r'(\d+)', filename)]

    def import_files(self):
        """导入多个文件"""
        file_paths = filedialog.askopenfilenames(
            title="选择文本文件",
            filetypes=[
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ]
        )

        if file_paths:
            self.add_files(list(file_paths))

    def import_folder(self):
        """导入文件夹中的所有文本文件"""
        folder_path = filedialog.askdirectory(title="选择文件夹")

        if folder_path:
            txt_files = []

            # 遍历文件夹中的文件
            for root, _, files in os.walk(folder_path):
                for file in files:
                    if file.lower().endswith('.txt'):
                        txt_files.append(os.path.join(root, file))

            if txt_files:
                # 在添加之前先按自然顺序排序，确保文件顺序正确
                txt_files.sort(key=lambda x: self.natural_sort_key(os.path.basename(x)))
                self.add_files(txt_files)
            else:
                messagebox.showinfo("提示", "所选文件夹中没有找到 .txt 文件")

    def add_files(self, file_paths):
        """添加文件到列表"""
        # 过滤出不在列表中的文件
        new_files = [file_path for file_path in file_paths if file_path not in self.file_paths]

        if new_files:
            # 添加新文件到列表
            self.file_paths.extend(new_files)

            # 对整个文件列表按文件名进行自然排序
            self.file_paths.sort(key=lambda x: self.natural_sort_key(os.path.basename(x)))

            # 重新创建整个文件列表显示
            self.refresh_file_list()

        self.update_status()

    def refresh_file_list(self):
        """刷新文件列表显示"""
        # 清空当前的文件变量列表
        self.file_vars.clear()

        # 清空界面
        for widget in self.file_list_frame.winfo_children():
            widget.destroy()

        # 重新创建所有文件项
        for file_path in self.file_paths:
            self.create_file_item(file_path)

        # 更新滚动区域
        self.file_list_canvas.configure(scrollregion=self.file_list_canvas.bbox("all"))

    def create_file_item(self, file_path):
        """创建文件列表项"""
        # 创建文件项框架
        item_frame = tk.Frame(self.file_list_frame, bg="white", pady=2)
        item_frame.pack(fill=tk.X, padx=5, pady=1)

        # 创建复选框变量
        var = tk.BooleanVar(value=True)
        self.file_vars.append(var)

        # 复选框
        checkbox = tk.Checkbutton(
            item_frame,
            variable=var,
            bg="white",
            command=self.update_status
        )
        checkbox.pack(side=tk.LEFT)

        # 文件信息框架
        info_frame = tk.Frame(item_frame, bg="white")
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # 文件名
        file_name = os.path.basename(file_path)
        name_label = tk.Label(
            info_frame,
            text=file_name,
            font=("微软雅黑", 9, "bold"),
            fg=self.text_color,
            bg="white",
            anchor="w"
        )
        name_label.pack(fill=tk.X)

        # 文件路径
        path_label = tk.Label(
            info_frame,
            text=file_path,
            font=("微软雅黑", 8),
            fg="#6c757d",
            bg="white",
            anchor="w"
        )
        path_label.pack(fill=tk.X)

        # 文件信息
        file_info = self.get_file_info(file_path)
        info_label = tk.Label(
            info_frame,
            text=f"字符数: {file_info['char_count_str']} | 修改时间: {file_info['modified']}",
            font=("微软雅黑", 8),
            fg="#6c757d",
            bg="white",
            anchor="w"
        )
        info_label.pack(fill=tk.X)

        # 更新滚动区域
        self.file_list_frame.update_idletasks()
        self.file_list_canvas.configure(scrollregion=self.file_list_canvas.bbox("all"))

    def get_file_info(self, file_path):
        """获取文件信息"""
        try:
            # 获取文件大小和修改时间
            stat = os.stat(file_path)
            modified = datetime.datetime.fromtimestamp(stat.st_mtime).strftime("%Y-%m-%d %H:%M")

            # 读取文件内容获取字符数
            content = self._read_file_with_encoding_detection(file_path)
            char_count = len(content)
            char_count_str = f"{char_count:,}"

            return {
                "char_count": char_count,
                "char_count_str": char_count_str,
                "modified": modified
            }
        except Exception:
            return {
                "char_count": 0,
                "char_count_str": "读取错误",
                "modified": "未知"
            }

    def _read_file_with_encoding_detection(self, file_path):
        """
        智能读取文件，使用编码检测

        参数:
            file_path (str): 文件路径

        返回:
            str: 文件内容
        """
        try:
            # 首先尝试使用chardet检测编码
            encoding_info = self._detect_file_encoding(file_path)
            detected_encoding = encoding_info['encoding']
            confidence = encoding_info['confidence']

            if detected_encoding and confidence > 0.7:
                # 如果检测到的编码置信度较高，使用检测到的编码
                try:
                    with open(file_path, 'r', encoding=detected_encoding) as f:
                        content = f.read()
                    return content

                except Exception as e:
                    print(f"使用检测到的编码 {detected_encoding} 读取失败: {e}")

            # 如果chardet检测失败或置信度低，尝试常用编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'utf-16', 'latin-1', 'cp1252']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()

                    # 简单验证内容是否正常（检查是否有过多的替换字符）
                    if self._validate_content(content):
                        return content

                except Exception:
                    continue

            # 如果所有编码都失败，使用二进制模式读取
            with open(file_path, 'rb') as f:
                raw_data = f.read()

            # 尝试用utf-8解码，替换无法解码的字符
            content = raw_data.decode('utf-8', errors='replace')
            return content

        except Exception as e:
            raise Exception(f"无法读取文件: {str(e)}")

    def _detect_file_encoding(self, file_path):
        """
        使用chardet检测文件编码

        参数:
            file_path (str): 文件路径

        返回:
            dict: 包含编码信息的字典
        """
        try:
            # 尝试导入chardet
            import chardet
        except ImportError:
            # 如果没有安装chardet，返回默认值
            return {'encoding': None, 'confidence': 0}

        try:
            # 读取文件的一部分来检测编码
            with open(file_path, 'rb') as f:
                # 读取前10KB来检测编码，对于大文件这样更高效
                raw_data = f.read(10240)
                if len(raw_data) < 10240:
                    # 如果文件小于10KB，读取全部
                    f.seek(0)
                    raw_data = f.read()

            # 使用chardet检测编码
            result = chardet.detect(raw_data)
            return result

        except Exception as e:
            print(f"编码检测失败: {e}")
            return {'encoding': None, 'confidence': 0}

    def _validate_content(self, content):
        """
        验证文件内容是否正常

        参数:
            content (str): 文件内容

        返回:
            bool: 内容是否正常
        """
        if not content:
            return True

        # 检查替换字符的比例
        replacement_chars = content.count('�')
        total_chars = len(content)

        if total_chars == 0:
            return True

        # 如果替换字符超过5%，认为编码可能不正确
        replacement_ratio = replacement_chars / total_chars
        return replacement_ratio < 0.05

    def select_all_files(self):
        """全选文件"""
        for var in self.file_vars:
            var.set(True)
        self.update_status()

    def deselect_all_files(self):
        """全不选文件"""
        for var in self.file_vars:
            var.set(False)
        self.update_status()

    def clear_file_list(self):
        """清空文件列表"""
        # 清空文件路径和变量
        self.file_paths.clear()
        self.file_vars.clear()

        # 清空界面
        for widget in self.file_list_frame.winfo_children():
            widget.destroy()
    def _on_drop_files(self, event):
        """处理从资源管理器拖拽进来的文件/文件夹（tkinterdnd2）"""
        print("🎯 AI配音模块：拖拽事件被触发！")
        try:
            data = event.data
            print(f"📁 AI配音模块：拖拽数据: {data}")
            if not data:
                print("❌ AI配音模块：拖拽数据为空")
                return
            # tkinterdnd2 的文件列表可能带有大括号包裹路径，需特殊解析
            import re
            # 使用正则表达式提取大括号内的完整路径
            paths = re.findall(r'\{([^}]+)\}', data)
            if not paths:
                # 如果没有大括号，尝试按空格分割（但要小心路径中的空格）
                import shlex
                try:
                    paths = shlex.split(data, posix=False)
                except Exception:
                    paths = [data.strip()]
            print(f"📂 AI配音模块：解析后的路径列表: {paths}")

            import os
            txts = []
            for p in paths:
                p = p.strip()
                print(f"🔍 AI配音模块：处理路径: {p}")
                # 去掉大括号包装
                if p.startswith("{") and p.endswith("}"):
                    p = p[1:-1]
                    print(f"🔧 AI配音模块：去掉大括号后: {p}")
                if os.path.isdir(p):
                    print(f"📁 AI配音模块：这是一个文件夹，搜索txt文件...")
                    for root, _, files in os.walk(p):
                        for f in files:
                            if f.lower().endswith('.txt'):
                                txt_file = os.path.join(root, f)
                                txts.append(txt_file)
                                print(f"✅ AI配音模块：找到txt文件: {txt_file}")
                else:
                    if p.lower().endswith('.txt'):
                        txts.append(p)
                        print(f"✅ AI配音模块：找到txt文件: {p}")
                    else:
                        print(f"❌ AI配音模块：不是txt文件: {p}")

            print(f"📋 AI配音模块：总共找到 {len(txts)} 个txt文件")
            if txts:
                # 自然排序后添加
                txts.sort(key=lambda x: self.natural_sort_key(os.path.basename(x)))
                print(f"🎉 AI配音模块：开始添加文件到列表...")
                self.add_files(txts)
                print(f"✅ AI配音模块：成功添加 {len(txts)} 个文件")
            else:
                print("💡 AI配音模块：没有找到txt文件")
        except Exception as e:
            print(f"❌ AI配音模块：拖拽导入失败: {e}")
            import traceback
            traceback.print_exc()


        # 更新滚动区域
        self.file_list_canvas.configure(scrollregion=self.file_list_canvas.bbox("all"))
        self.update_status()

    def resort_file_list(self):
        """重新按自然顺序排序文件列表"""
        if not self.file_paths:
            return

        # 保存当前选中状态
        selected_states = [var.get() for var in self.file_vars]

        # 创建文件路径和选中状态的配对列表
        file_state_pairs = list(zip(self.file_paths, selected_states))

        # 按文件名进行自然排序
        file_state_pairs.sort(key=lambda x: self.natural_sort_key(os.path.basename(x[0])))

        # 分离排序后的文件路径和选中状态
        self.file_paths, selected_states = zip(*file_state_pairs)
        self.file_paths = list(self.file_paths)

        # 重新创建文件列表显示
        self.refresh_file_list()

        # 恢复选中状态
        for i, state in enumerate(selected_states):
            if i < len(self.file_vars):
                self.file_vars[i].set(state)

        self.update_status()

    def get_selected_file_paths(self):
        """获取选中的文件路径"""
        selected_paths = []
        for i, var in enumerate(self.file_vars):
            if var.get() and i < len(self.file_paths):
                selected_paths.append(self.file_paths[i])
        return selected_paths

    def update_status(self):
        """更新状态栏"""
        total_files = len(self.file_paths)
        selected_files = len(self.get_selected_file_paths())

        if total_files == 0:
            self.status_label.config(text="就绪")
        else:
            self.status_label.config(text=f"共 {total_files} 个文件，已选择 {selected_files} 个")

    def select_output_dir(self):
        """选择输出目录"""
        dir_path = filedialog.askdirectory(title="选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.output_dir_var.set(dir_path)
            # 保存到配置
            config_manager.set_last_output_dir(dir_path)

    def select_ffmpeg_path(self):
        """选择FFmpeg路径"""
        dir_path = filedialog.askdirectory(title="选择FFmpeg目录（包含bin文件夹的目录）")
        if dir_path:
            # 设置FFmpeg路径
            if self.processor.set_ffmpeg_path(dir_path):
                self.ffmpeg_path_var.set(dir_path)
                self.ffmpeg_status_label.config(
                    text="FFmpeg已设置，将使用高质量音频合并",
                    fg="#28a745"
                )
            else:
                messagebox.showerror(
                    "错误",
                    "所选目录中未找到有效的FFmpeg程序\n请确保目录结构为：\n选择的目录/bin/ffmpeg.exe"
                )
                self.ffmpeg_status_label.config(
                    text="FFmpeg路径无效，将使用简单合并方式",
                    fg="#dc3545"
                )

    def open_advanced_settings(self):
        """打开高级设置对话框"""
        dialog = AdvancedSettingsDialog(self.frame)
        if dialog.result:
            # 用户确认了设置，更新处理器的所有设置
            self.processor.update_settings_from_config()
            # 显示设置已保存的提示
            self.status_label.config(text="高级设置已保存")



    def start_processing(self):
        """开始处理文件"""
        selected_paths = self.get_selected_file_paths()

        if not selected_paths:
            messagebox.showinfo("提示", "请先选择要处理的文件")
            return

        if not self.output_dir:
            messagebox.showinfo("提示", "请先选择输出目录")
            return

        # 保存当前设置
        self.save_current_settings()

        # 更新处理器设置
        self.processor.update_settings_from_config()

        # 开始处理
        self._start_processing_thread(selected_paths)

    def _start_processing_thread(self, file_paths):
        """启动处理线程"""
        if self.is_processing:
            return

        # 获取配音参数
        voice = self.get_selected_voice_id()
        rate = self.processor.convert_rate(self.rate_var.get())
        pitch = self.processor.convert_pitch(self.pitch_var.get())
        volume = self.processor.convert_volume(self.volume_var.get())
        volume_raw = self.volume_var.get()  # 保存原始音量值用于后处理
        generate_srt = self.generate_srt_var.get()

        # 设置处理状态
        self.is_processing = True
        self.is_paused = False

        # 重置进度显示
        self.reset_progress()

        # 启动计时器
        self.start_timer()

        # 更新按钮状态
        self.process_btn.config(state=tk.DISABLED)
        self.import_files_btn.config(state=tk.DISABLED)
        self.import_folder_btn.config(state=tk.DISABLED)
        self.pause_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.NORMAL)

        # 更新状态
        self.status_label.config(text=f"开始处理 {len(file_paths)} 个文件...")

        # 创建并启动处理线程
        self.processing_thread = threading.Thread(
            target=self._process_files_thread,
            args=(file_paths, voice, rate, pitch, volume, generate_srt, volume_raw)
        )
        self.processing_thread.daemon = True
        self.processing_thread.start()

        # 启动超时监控
        self.start_timeout_monitor()

    def _process_files_thread(self, file_paths, voice, rate, pitch, volume, generate_srt, volume_raw):
        """处理文件的后台线程"""
        try:
            def progress_callback(progress_info):
                # 始终将UI更新调度到主线程，避免跨线程更新导致异常/卡死
                if self.is_processing:  # 检查是否还在处理中
                    try:
                        self.frame.after(0, lambda info=progress_info: self.update_progress(info))
                    except Exception as _:
                        # 忽略调度异常，避免影响后续处理
                        pass

            # 处理文件
            results = self.processor.process_files(
                file_paths, self.output_dir, voice, rate, pitch, volume,
                generate_srt, progress_callback, volume_raw
            )

            if not self.is_processing:
                # 用户停止了处理
                return

            # 停止计时器
            self.stop_timer()

            # 统计结果
            success_count = sum(1 for r in results if r['success'])
            failed_count = len(results) - success_count

            # 显示结果
            if failed_count == 0:
                message = f"处理完成！成功处理 {success_count} 个文件"
                messagebox.showinfo("处理完成", message)
            else:
                message = f"处理完成！成功 {success_count} 个，失败 {failed_count} 个"

                # 显示失败的文件
                failed_files = [r['file_path'] for r in results if not r['success']]
                error_details = "\n".join([f"- {os.path.basename(f)}" for f in failed_files[:5]])
                if len(failed_files) > 5:
                    error_details += f"\n... 还有 {len(failed_files) - 5} 个文件"

                messagebox.showwarning("处理完成", f"{message}\n\n失败的文件:\n{error_details}")

            self.status_label.config(text="处理完成")

            # 清理FFmpeg进程
            self.processor.cleanup_on_exit()

        except Exception as e:
            if self.is_processing:  # 只有在还在处理时才显示错误
                messagebox.showerror("处理错误", f"处理过程中发生错误: {str(e)}")
                self.status_label.config(text="处理失败")

        finally:
            # 重置处理状态 - 必须在显示弹窗之前停止超时监控
            self.is_processing = False
            self.stop_timer()
            self.stop_timeout_monitor()

            # 重新启用按钮
            self.process_btn.config(state=tk.NORMAL)
            self.import_files_btn.config(state=tk.NORMAL)
            self.import_folder_btn.config(state=tk.NORMAL)
            self.stop_btn.config(state=tk.DISABLED)
            self.pause_btn.config(state=tk.DISABLED, text="暂停", bg="#ffc107")

    def start_timeout_monitor(self):
        """启动超时监控"""
        self.last_progress_time = time.time()
        self.timeout_monitor_thread = threading.Thread(target=self._timeout_monitor_thread, daemon=True)
        self.timeout_monitor_thread.start()

    def _timeout_monitor_thread(self):
        """智能超时监控线程"""
        check_count = 0
        last_update_count = 0

        while self.is_processing:
            try:
                current_time = time.time()
                check_count += 1

                # 检查是否有真正的进度更新（不仅仅是时间）
                has_real_progress = self.progress_update_count > last_update_count
                last_update_count = self.progress_update_count

                # 如果有真正的进度更新，重置超时计时
                if has_real_progress:
                    self.last_progress_time = current_time

                # 检查处理状态，如果已经不在处理中，立即退出
                if not self.is_processing:
                    print("超时监控检测到处理已完成，退出监控线程")
                    break

                # 只有在真正长时间没有任何进度更新时才触发超时
                if self.last_progress_time and (current_time - self.last_progress_time) > self.timeout_threshold:
                    # 再次确认是否真的卡住了，并且确认还在处理中
                    if not has_real_progress and self.progress_update_count == last_update_count and self.is_processing:
                        print(f"检测到真正超时: {current_time - self.last_progress_time:.1f}秒 > {self.timeout_threshold}秒")
                        print(f"进度更新计数: {self.progress_update_count}, 上次计数: {last_update_count}")
                        print(f"处理状态: {self.is_processing}")
                        self.handle_timeout()
                        break

                # 每10秒检查一次，减少CPU占用
                time.sleep(10)

                # 每2分钟输出一次监控状态（调试用）
                if check_count % 12 == 0:  # 10秒 * 12 = 120秒
                    if self.last_progress_time:
                        elapsed = current_time - self.last_progress_time
                        print(f"超时监控: 距离上次进度更新 {elapsed:.1f}秒 (阈值: {self.timeout_threshold}秒), 进度计数: {self.progress_update_count}")

            except Exception as e:
                print(f"超时监控线程异常: {e}")
                break

        print("超时监控线程已退出")

    def handle_timeout(self):
        """处理超时情况"""
        try:
            # 在主线程中执行UI更新
            self.frame.after(0, self._handle_timeout_ui)
        except Exception as e:
            print(f"处理超时异常: {e}")

    def _handle_timeout_ui(self):
        """在主线程中处理超时UI更新"""
        try:
            # 检查是否真的还在处理中
            if not self.is_processing:
                print("超时检测触发，但处理已完成，忽略超时")
                return

            # 再次确认状态，避免误触发
            if hasattr(self, 'status_label'):
                current_status = self.status_label.cget('text')
                if '处理完成' in current_status or '已停止' in current_status:
                    print(f"超时检测触发，但状态显示已完成: {current_status}，忽略超时")
                    return

            # 显示超时提示
            self.status_label.config(text="检测到操作超时，正在重启处理...")

            # 强制停止当前处理
            self.processor.stop_processing()
            self.is_processing = False

            # 等待一下让停止操作完成
            self.frame.after(2000, self._restart_after_timeout)

        except Exception as e:
            print(f"超时UI处理异常: {e}")

    def _restart_after_timeout(self):
        """超时后重启处理"""
        try:
            # 超时后不再自动重启，直接提示用户手动重新开始
            self._handle_restart_failure("超时检测到处理卡住")

        except Exception as e:
            print(f"超时重启异常: {e}")
            self._handle_restart_failure(f"重启异常: {str(e)}")

    def _handle_restart_failure(self, reason):
        """处理重启失败的情况"""
        self.status_label.config(text=f"超时重启失败: {reason}，请手动重新开始")

        # 重新启用按钮
        self.process_btn.config(state=tk.NORMAL)
        self.import_files_btn.config(state=tk.NORMAL)
        self.import_folder_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)

        # 显示详细的错误信息
        messagebox.showwarning(
            "处理超时",
            f"处理过程中发生超时，自动重启失败。\n\n"
            f"失败原因：{reason}\n\n"
            f"超时阈值：{self.timeout_threshold}秒 (已优化为20分钟)\n\n"
            f"建议操作：\n"
            f"1. 检查网络连接是否稳定（edge-tts需要网络）\n"
            f"2. 尝试减少分块大小（在高级设置中）\n"
            f"3. 减少并发线程数（在高级设置中）\n"
            f"4. 手动重新开始处理\n"
            f"5. 如果问题持续，请检查FFmpeg配置\n"
            f"6. 可以使用暂停功能分段处理"
        )

    def stop_timeout_monitor(self):
        """停止超时监控"""
        print("正在停止超时监控...")
        self.last_progress_time = None
        # 确保is_processing为False，超时监控线程会自动退出
        # 注意：这里不直接设置is_processing，因为调用者会设置
        print("超时监控停止请求已发送")
