"""
TTS详细日志记录器
记录TTS处理的每一步执行过程，用于调试和问题排查
"""

import os
import time
import threading
from datetime import datetime
from common.path_utils import get_log_path, ensure_dir_exists

class TTSLogger:
    """TTS详细日志记录器"""
    
    def __init__(self):
        self.log_lock = threading.Lock()
        self.log_dir = get_log_path()
        ensure_dir_exists(self.log_dir)
        
        # TTS专用日志文件
        self.tts_log_file = os.path.join(self.log_dir, "tts_detailed.log")
        
        # 初始化日志文件
        self._init_log_file()
    
    def _init_log_file(self):
        """初始化日志文件"""
        try:
            with self.log_lock:
                with open(self.tts_log_file, 'w', encoding='utf-8') as f:
                    f.write(f"TTS详细日志 - 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 100 + "\n\n")
        except Exception as e:
            print(f"初始化TTS日志文件失败: {e}")
    
    def _write_log(self, level, message, details=None):
        """写入日志条目"""
        try:
            with self.log_lock:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 包含毫秒
                
                log_entry = f"[{timestamp}] [{level}] {message}\n"
                
                if details:
                    for key, value in details.items():
                        log_entry += f"  {key}: {value}\n"
                
                log_entry += "\n"
                
                with open(self.tts_log_file, 'a', encoding='utf-8') as f:
                    f.write(log_entry)
                    f.flush()  # 立即刷新到磁盘
                    
        except Exception as e:
            print(f"写入TTS日志失败: {e}")
    
    def log_segment_start(self, segment_index, segment_text, total_segments):
        """记录分段开始处理"""
        self._write_log("SEGMENT_START", f"开始处理分段 {segment_index + 1}/{total_segments}", {
            "分段索引": segment_index,
            "总分段数": total_segments,
            "分段文本长度": len(segment_text),
            "分段文本预览": segment_text[:100] + ("..." if len(segment_text) > 100 else "")
        })
    
    def log_tts_command_start(self, command_type, details):
        """记录TTS命令开始执行"""
        self._write_log("TTS_CMD_START", f"开始执行TTS命令 - {command_type}", details)
    
    def log_tts_command_progress(self, message):
        """记录TTS命令执行进度"""
        self._write_log("TTS_PROGRESS", message)
    
    def log_tts_command_end(self, command_type, success, execution_time, details=None):
        """记录TTS命令执行结束"""
        status = "成功" if success else "失败"
        log_details = {
            "执行结果": status,
            "执行时间": f"{execution_time:.3f}秒"
        }
        if details:
            log_details.update(details)
        
        self._write_log("TTS_CMD_END", f"TTS命令执行完成 - {command_type} - {status}", log_details)
    
    def log_timeout_detection(self, segment_index, execution_time, is_timeout):
        """记录超时检测结果"""
        status = "超时" if is_timeout else "正常"
        self._write_log("TIMEOUT_CHECK", f"分段 {segment_index + 1} 超时检测 - {status}", {
            "执行时间": f"{execution_time:.3f}秒",
            "是否超时": is_timeout,
            "超时阈值": "120秒"
        })
    
    def log_retry_attempt(self, segment_index, retry_type, retry_count):
        """记录重试尝试"""
        self._write_log("RETRY", f"分段 {segment_index + 1} {retry_type}重试", {
            "重试类型": retry_type,
            "重试次数": retry_count
        })
    
    def log_subprocess_start(self, cmd, cwd=None):
        """记录子进程开始"""
        self._write_log("SUBPROCESS_START", "开始执行子进程", {
            "命令": " ".join(cmd) if isinstance(cmd, list) else str(cmd),
            "工作目录": cwd or "当前目录"
        })
    
    def log_subprocess_end(self, returncode, stdout, stderr, execution_time):
        """记录子进程结束"""
        self._write_log("SUBPROCESS_END", f"子进程执行完成 - 返回码: {returncode}", {
            "返回码": returncode,
            "执行时间": f"{execution_time:.3f}秒",
            "标准输出": stdout[:500] + ("..." if len(stdout) > 500 else "") if stdout else "无",
            "错误输出": stderr[:500] + ("..." if len(stderr) > 500 else "") if stderr else "无"
        })
    
    def log_file_operation(self, operation, file_path, success, details=None):
        """记录文件操作"""
        status = "成功" if success else "失败"
        log_details = {
            "文件路径": file_path,
            "操作结果": status
        }
        if details:
            log_details.update(details)
        
        self._write_log("FILE_OP", f"文件操作 - {operation} - {status}", log_details)
    
    def log_network_operation(self, operation, details):
        """记录网络操作"""
        self._write_log("NETWORK", f"网络操作 - {operation}", details)
    
    def log_error(self, error_type, error_message, exception=None, details=None):
        """记录错误"""
        log_details = {
            "错误类型": error_type,
            "错误消息": error_message
        }
        
        if exception:
            log_details["异常类型"] = type(exception).__name__
            log_details["异常详情"] = str(exception)
        
        if details:
            log_details.update(details)
        
        self._write_log("ERROR", f"发生错误 - {error_type}", log_details)
    
    def log_step(self, step_name, details=None):
        """记录处理步骤"""
        self._write_log("STEP", step_name, details)
    
    def log_performance(self, operation, start_time, end_time, details=None):
        """记录性能信息"""
        execution_time = end_time - start_time
        log_details = {
            "执行时间": f"{execution_time:.3f}秒",
            "开始时间": datetime.fromtimestamp(start_time).strftime("%H:%M:%S.%f")[:-3],
            "结束时间": datetime.fromtimestamp(end_time).strftime("%H:%M:%S.%f")[:-3]
        }
        if details:
            log_details.update(details)
        
        self._write_log("PERFORMANCE", f"性能统计 - {operation}", log_details)
    
    def log_segment_complete(self, segment_index, success, total_time, details=None):
        """记录分段处理完成"""
        status = "成功" if success else "失败"
        log_details = {
            "处理结果": status,
            "总耗时": f"{total_time:.3f}秒"
        }
        if details:
            log_details.update(details)
        
        self._write_log("SEGMENT_COMPLETE", f"分段 {segment_index + 1} 处理完成 - {status}", log_details)
        
        # 添加分隔线
        with self.log_lock:
            with open(self.tts_log_file, 'a', encoding='utf-8') as f:
                f.write("-" * 80 + "\n\n")
    
    def get_log_path(self):
        """获取日志文件路径"""
        return self.tts_log_file
    
    def clear_log(self):
        """清空日志文件"""
        self._init_log_file()

# 全局TTS日志记录器实例
tts_logger = TTSLogger()
